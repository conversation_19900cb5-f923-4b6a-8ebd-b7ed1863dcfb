@page "/welcome"

<div class="welcome-container">
    <!-- Main Content -->
    <div class="content">
        <!-- Logo Section -->
        <div class="logo-section">
            <div class="logo-icon">
                <svg width="80" height="60" viewBox="0 0 80 60" fill="none">
                    <!-- Building bars -->
                    <rect x="10" y="25" width="12" height="30" fill="#4ECDC4" rx="2"/>
                    <rect x="25" y="20" width="12" height="35" fill="#4ECDC4" rx="2"/>
                    <rect x="40" y="15" width="12" height="40" fill="#4ECDC4" rx="2"/>
                    <rect x="55" y="22" width="12" height="33" fill="#4ECDC4" rx="2"/>
                    <!-- Base platform -->
                    <path d="M5 55 L75 55 L70 58 L10 58 Z" fill="#FF6B6B"/>
                    <!-- Small house on top -->
                    <path d="M35 10 L45 10 L50 5 L40 5 L35 10 Z" fill="#FF6B6B"/>
                </svg>
            </div>
            <h1 class="app-title">
                <span class="gate">GATE</span><span class="sale">SALE</span>
            </h1>
        </div>

        <!-- Tagline -->
        <p class="tagline">A safe marketplace just for students.</p>

        <!-- Illustration -->
        <div class="illustration-container">
            <div class="background-square"></div>
            <div class="illustration">
                <!-- Left Student (Boy with brown hair, teal shirt, red backpack) -->
                <div class="student student-left">
                    <div class="head">
                        <div class="hair hair-brown"></div>
                        <div class="face"></div>
                        <div class="eyes">
                            <div class="eye eye-left"></div>
                            <div class="eye eye-right"></div>
                        </div>
                        <div class="mouth"></div>
                    </div>
                    <div class="body">
                        <div class="shirt shirt-teal"></div>
                        <div class="arms">
                            <div class="arm arm-left"></div>
                            <div class="arm arm-right holding-item"></div>
                        </div>
                    </div>
                    <div class="legs">
                        <div class="pants pants-beige"></div>
                    </div>
                    <div class="backpack backpack-red"></div>
                </div>

                <!-- Right Student (Girl with dark curly hair, orange shirt) -->
                <div class="student student-right">
                    <div class="head">
                        <div class="hair hair-dark-curly"></div>
                        <div class="face face-dark"></div>
                        <div class="eyes">
                            <div class="eye eye-left"></div>
                            <div class="eye eye-right"></div>
                        </div>
                        <div class="mouth"></div>
                    </div>
                    <div class="body">
                        <div class="shirt shirt-orange"></div>
                        <div class="arms">
                            <div class="arm arm-left receiving-item"></div>
                            <div class="arm arm-right"></div>
                        </div>
                    </div>
                    <div class="legs">
                        <div class="pants pants-dark"></div>
                    </div>
                </div>

                <!-- Exchange Items -->
                <div class="exchange-items">
                    <div class="item-being-exchanged"></div>
                </div>
            </div>

            <!-- Floating Hearts -->
            <div class="floating-element element-1">💚</div>
            <div class="floating-element element-2">💙</div>
        </div>

        <!-- Next Button -->
        <button class="next-button">Next</button>

        <!-- Skip Text -->
        <p class="skip-text">Skip for now</p>

        <!-- Page Indicators -->
        <div class="page-indicators">
            <span class="indicator active"></span>
            <span class="indicator"></span>
            <span class="indicator"></span>
        </div>
    </div>
</div>

<style>
    .welcome-container {
        width: 375px;
        height: 812px;
        background: #FFFFFF;
        margin: 0 auto;
        position: relative;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        overflow: hidden;
    }

    /* Content */
    .content {
        padding: 60px 30px 30px 30px;
        display: flex;
        flex-direction: column;
        align-items: center;
        height: 100%;
    }

    /* Logo Section */
    .logo-section {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-bottom: 20px;
    }

    .logo-icon {
        margin-bottom: 15px;
    }

    .app-title {
        font-size: 32px;
        font-weight: 700;
        margin: 0;
        letter-spacing: 1px;
    }

    .gate {
        color: #4ECDC4;
    }

    .sale {
        color: #FF6B6B;
    }

    /* Tagline */
    .tagline {
        font-size: 16px;
        color: #666666;
        text-align: center;
        margin: 0 0 60px 0;
        line-height: 1.4;
    }

    /* Illustration Container */
    .illustration-container {
        position: relative;
        width: 280px;
        height: 280px;
        margin-bottom: 60px;
    }

    .background-square {
        position: absolute;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #E8F8F5 0%, #E1F5FE 100%);
        border-radius: 20px;
        top: 0;
        left: 0;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }

    .illustration {
        position: relative;
        width: 100%;
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 2;
    }

    /* Students */
    .student {
        position: absolute;
        width: 90px;
        height: 140px;
    }

    .student-left {
        left: 50px;
        top: 70px;
    }

    .student-right {
        right: 50px;
        top: 70px;
    }

    /* Student Parts */
    .head {
        position: relative;
        width: 45px;
        height: 45px;
        margin: 0 auto 8px;
    }

    .hair {
        position: absolute;
        width: 45px;
        height: 35px;
        border-radius: 22px 22px 15px 15px;
        top: 0;
    }

    .hair-brown {
        background: #8B4513;
    }

    .hair-dark-curly {
        background: #2C1810;
        border-radius: 22px 22px 20px 20px;
    }

    .face {
        position: absolute;
        width: 38px;
        height: 38px;
        background: #FDBCB4;
        border-radius: 19px;
        top: 12px;
        left: 3.5px;
    }

    .face-dark {
        background: #8D5524;
    }

    .eyes {
        position: absolute;
        top: 18px;
        left: 8px;
        width: 30px;
        height: 8px;
    }

    .eye {
        position: absolute;
        width: 4px;
        height: 4px;
        background: #000;
        border-radius: 50%;
    }

    .eye-left {
        left: 6px;
    }

    .eye-right {
        right: 6px;
    }

    .mouth {
        position: absolute;
        top: 26px;
        left: 16px;
        width: 8px;
        height: 4px;
        background: #FF6B6B;
        border-radius: 0 0 8px 8px;
    }

    .body {
        position: relative;
        width: 50px;
        height: 55px;
        margin: 0 auto;
    }

    .shirt {
        width: 50px;
        height: 45px;
        border-radius: 10px 10px 0 0;
    }

    .shirt-teal {
        background: #20B2AA;
    }

    .shirt-orange {
        background: #FF8C00;
    }

    .arms {
        position: absolute;
        top: 10px;
        width: 100%;
        height: 30px;
    }

    .arm {
        position: absolute;
        width: 15px;
        height: 35px;
        background: #FDBCB4;
        border-radius: 7px;
    }

    .arm-left {
        left: -8px;
        transform: rotate(-20deg);
    }

    .arm-right {
        right: -8px;
        transform: rotate(20deg);
    }

    .holding-item {
        background: #FDBCB4;
    }

    .receiving-item {
        background: #8D5524;
    }

    .legs {
        position: relative;
        width: 45px;
        height: 40px;
        margin: 0 auto;
    }

    .pants {
        width: 45px;
        height: 35px;
        border-radius: 0 0 10px 10px;
    }

    .pants-beige {
        background: #D2B48C;
    }

    .pants-dark {
        background: #2F4F4F;
    }

    .backpack {
        position: absolute;
        width: 25px;
        height: 30px;
        border-radius: 6px;
        top: 45px;
        right: -10px;
    }

    .backpack-red {
        background: #DC143C;
    }

    /* Exchange Items */
    .exchange-items {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 3;
    }

    .item-being-exchanged {
        width: 30px;
        height: 30px;
        background: linear-gradient(45deg, #FFD700, #FFA500);
        border-radius: 6px;
        border: 2px solid #FF8C00;
        box-shadow: 0 2px 8px rgba(255, 165, 0, 0.3);
    }

    /* Floating Elements */
    .floating-element {
        position: absolute;
        font-size: 20px;
        z-index: 1;
    }

    .element-1 {
        top: 30px;
        right: 40px;
    }

    .element-2 {
        bottom: 40px;
        left: 30px;
    }

    /* Next Button */
    .next-button {
        width: 280px;
        height: 50px;
        background: #00BFFF;
        border: none;
        border-radius: 25px;
        color: #FFFFFF;
        font-size: 18px;
        font-weight: 600;
        cursor: pointer;
        margin-bottom: 20px;
        transition: background-color 0.2s;
    }

    .next-button:hover {
        background: #0099CC;
    }

    /* Skip Text */
    .skip-text {
        font-size: 16px;
        color: #999999;
        margin: 0 0 30px 0;
        cursor: pointer;
    }

    /* Page Indicators */
    .page-indicators {
        display: flex;
        gap: 8px;
        margin-top: auto;
    }

    .indicator {
        width: 8px;
        height: 8px;
        border-radius: 4px;
        background: #E0E0E0;
        transition: background-color 0.2s;
    }

    .indicator.active {
        background: #00BFFF;
        width: 24px;
        border-radius: 4px;
    }

    /* Responsive adjustments */
    @@media (max-width: 375px) {
        .welcome-container {
            width: 100%;
            height: 100vh;
        }
    }
</style>
