{"extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\138.0.3351.83\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "protection": {"macs": {"browser": {"show_home_button": "B50ED3ED1576BFAD4C49D33E3680B2E61BA84673CD5C642CDD826570FE176A68"}, "default_search_provider_data": {"template_url_data": "1E23C53FEBD83A5C03147BBC32BB85AA50B75D4181F8C3770C0BEB55B8A01507"}, "edge": {"services": {"account_id": "3A26CD8BDBEDC6480EF8DEAC39EE468B82D3FF8D294E61F6F61D2276D5C93DE2", "last_username": "96A0F797D3CECC511736FEA65124626384DDE009B4BB75038099D426233FA30E"}}, "enterprise_signin": {"policy_recovery_token": "7DD460410B29F33D0BA23D260DAE5312D4249E45300740477725B989BB56E9B2"}, "extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": "5115356DE90074765A898EE4885EAD93C6541BF7A820F7D2F695E17278E52DE8", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "C99FB908F9A2F739636A1A7B948093D476F4367D8CA4629831E271B09278F9AD"}, "ui": {"developer_mode": "5BADDA460699FC84182F9EB5B590D8F952BE25E6B5EA4A97D19814F1A1C6968D"}}, "google": {"services": {"last_signed_in_username": "C5F50A2860F8E9AD83931811EBB5B41E26897911B7C3FEC7EBFB4B3EC714E360"}}, "homepage": "5ABAC792EE3C2B3ACEA26719E1C6D59CFECC80E0B597ACA8BCB0D64E5CC0B880", "homepage_is_newtabpage": "9F8538CF21BE13C5E80E8C4E60FDEF68655A6579AD48A9FAED43504FC8A007A2", "media": {"cdm": {"origin_data": "43C7CCE9678C033E3A37B7379E89A6605F574AFE238109F37034CBE04C6BA4C5"}, "storage_id_salt": "73047ACF99E7B251882852F978C7F679E5C3B6341959A41DA7958F75D2F4E4FD"}, "pinned_tabs": "A3E90228E33A917E33D880426E216EEA02695F83D8090D7E4A869FF1EDBBC1E4", "prefs": {"preference_reset_time": "D0E37B2A0B49A97F290EFA6A76375604E0FBF54D678D9C8DB37B6101C36C96C6"}, "safebrowsing": {"incidents_sent": "A18EA20B9CA58D552469894DD2B68AE70A170C5A47E2EBB6B0BE517BC4E2663B"}, "search_provider_overrides": "4D30709B916E1A89633F59303FE82664EE8925444AEE7DEBC67C0727CD4994ED", "session": {"restore_on_startup": "E254165C3080F97E5B51E6A00BE70AFF88985E23697091DA992F43126E5FDAAC", "startup_urls": "D58C658240CD401EB3CBBEF6EC61B1C5F1C0B3D388019BDBBBADAABE967B7248"}}, "super_mac": "5F70F978818503018CD7C5BBD6B1B44A2AACE28CB37AB6FC7E10A526C196BB70"}}