/* /Components/Layout/MainLayout.razor.rz.scp.css */
.page[b-cpoxglvm5c] {
    position: relative;
    display: flex;
    flex-direction: column;
}

main[b-cpoxglvm5c] {
    flex: 1;
}

.sidebar[b-cpoxglvm5c] {
    background-image: linear-gradient(180deg, rgb(5, 39, 103) 0%, #3a0647 70%);
}

.top-row[b-cpoxglvm5c] {
    background-color: #f7f7f7;
    border-bottom: 1px solid #d6d5d5;
    justify-content: flex-end;
    height: 3.5rem;
    display: flex;
    align-items: center;
}

    .top-row[b-cpoxglvm5c]  a, .top-row[b-cpoxglvm5c]  .btn-link {
        white-space: nowrap;
        margin-left: 1.5rem;
        text-decoration: none;
    }

        .top-row[b-cpoxglvm5c]  a:hover, .top-row[b-cpoxglvm5c]  .btn-link:hover {
            text-decoration: underline;
        }

        .top-row[b-cpoxglvm5c]  a:first-child {
            overflow: hidden;
            text-overflow: ellipsis;
        }

@media (max-width: 640.98px) {
    .top-row[b-cpoxglvm5c] {
        justify-content: space-between;
    }

        .top-row[b-cpoxglvm5c]  a, .top-row[b-cpoxglvm5c]  .btn-link {
            margin-left: 0;
        }
}

@media (min-width: 641px) {
    .page[b-cpoxglvm5c] {
        flex-direction: row;
    }

    .sidebar[b-cpoxglvm5c] {
        width: 250px;
        height: 100vh;
        position: sticky;
        top: 0;
    }

    .top-row[b-cpoxglvm5c] {
        position: sticky;
        top: 0;
        z-index: 1;
    }

        .top-row.auth[b-cpoxglvm5c]  a:first-child {
            flex: 1;
            text-align: right;
            width: 0;
        }

    .top-row[b-cpoxglvm5c], article[b-cpoxglvm5c] {
        padding-left: 2rem !important;
        padding-right: 1.5rem !important;
    }
}
/* /Components/Layout/NavMenu.razor.rz.scp.css */
.navbar-toggler[b-o5lggcno8n] {
    appearance: none;
    cursor: pointer;
    width: 3.5rem;
    height: 2.5rem;
    color: white;
    position: absolute;
    top: 0.5rem;
    right: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
    background: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e") no-repeat center/1.75rem rgba(255, 255, 255, 0.1);
}

    .navbar-toggler:checked[b-o5lggcno8n] {
        background-color: rgba(255, 255, 255, 0.5);
    }

.top-row[b-o5lggcno8n] {
    height: 3.5rem;
    background-color: rgba(0,0,0,0.4);
}

.navbar-brand[b-o5lggcno8n] {
    font-size: 1.1rem;
}

.bi[b-o5lggcno8n] {
    display: inline-block;
    position: relative;
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.75rem;
    top: -1px;
    background-size: cover;
}

.bi-house-door-fill-nav-menu[b-o5lggcno8n] {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-house-door-fill' viewBox='0 0 16 16'%3E%3Cpath d='M6.5 14.5v-3.505c0-.245.25-.495.5-.495h2c.25 0 .5.25.5.5v3.5a.5.5 0 0 0 .5.5h4a.5.5 0 0 0 .5-.5v-7a.5.5 0 0 0-.146-.354L13 5.793V2.5a.5.5 0 0 0-.5-.5h-1a.5.5 0 0 0-.5.5v1.293L8.354 1.146a.5.5 0 0 0-.708 0l-6 6A.5.5 0 0 0 1.5 7.5v7a.5.5 0 0 0 .5.5h4a.5.5 0 0 0 .5-.5Z'/%3E%3C/svg%3E");
}

.bi-plus-square-fill-nav-menu[b-o5lggcno8n] {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-plus-square-fill' viewBox='0 0 16 16'%3E%3Cpath d='M2 0a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V2a2 2 0 0 0-2-2H2zm6.5 4.5v3h3a.5.5 0 0 1 0 1h-3v3a.5.5 0 0 1-1 0v-3h-3a.5.5 0 0 1 0-1h3v-3a.5.5 0 0 1 1 0z'/%3E%3C/svg%3E");
}

.bi-list-nested-nav-menu[b-o5lggcno8n] {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='white' class='bi bi-list-nested' viewBox='0 0 16 16'%3E%3Cpath fill-rule='evenodd' d='M4.5 11.5A.5.5 0 0 1 5 11h10a.5.5 0 0 1 0 1H5a.5.5 0 0 1-.5-.5zm-2-4A.5.5 0 0 1 3 7h10a.5.5 0 0 1 0 1H3a.5.5 0 0 1-.5-.5zm-2-4A.5.5 0 0 1 1 3h10a.5.5 0 0 1 0 1H1a.5.5 0 0 1-.5-.5z'/%3E%3C/svg%3E");
}

.nav-item[b-o5lggcno8n] {
    font-size: 0.9rem;
    padding-bottom: 0.5rem;
}

    .nav-item:first-of-type[b-o5lggcno8n] {
        padding-top: 1rem;
    }

    .nav-item:last-of-type[b-o5lggcno8n] {
        padding-bottom: 1rem;
    }

    .nav-item[b-o5lggcno8n]  a {
        color: #d7d7d7;
        border-radius: 4px;
        height: 3rem;
        display: flex;
        align-items: center;
        line-height: 3rem;
    }

        .nav-item[b-o5lggcno8n]  a.active {
            background-color: rgba(255,255,255,0.37);
            color: white;
        }

        .nav-item[b-o5lggcno8n]  a:hover {
            background-color: rgba(255,255,255,0.1);
            color: white;
        }

.nav-scrollable[b-o5lggcno8n] {
    display: none;
}

.navbar-toggler:checked ~ .nav-scrollable[b-o5lggcno8n] {
    display: block;
}

@media (min-width: 641px) {
    .navbar-toggler[b-o5lggcno8n] {
        display: none;
    }

    .nav-scrollable[b-o5lggcno8n] {
        /* Never collapse the sidebar for wide screens */
        display: block;
        /* Allow sidebar to scroll for tall menus */
        height: calc(100vh - 3.5rem);
        overflow-y: auto;
    }
}
