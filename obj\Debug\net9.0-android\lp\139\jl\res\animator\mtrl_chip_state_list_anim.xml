<?xml version="1.0" encoding="utf-8"?>
<!--
  Copyright 2017 The Android Open Source Project

  Licensed under the Apache License, Version 2.0 (the "License");
  you may not use this file except in compliance with the License.
  You may obtain a copy of the License at

      https://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing, software
  distributed under the License is distributed on an "AS IS" BASIS,
  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  See the License for the specific language governing permissions and
  limitations under the License.
  -->

<selector xmlns:android="http://schemas.android.com/apk/res/android">

  <!-- Pressed state -->
  <item
      android:state_enabled="true"
      android:state_pressed="true">
    <objectAnimator
        android:duration="@integer/mtrl_chip_anim_duration"
        android:propertyName="translationZ"
        android:valueTo="@dimen/mtrl_chip_pressed_translation_z"
        android:valueType="floatType"/>
  </item>

  <!-- Enabled state -->
  <item android:state_enabled="true">
    <objectAnimator
        android:duration="@integer/mtrl_chip_anim_duration"
        android:propertyName="translationZ"
        android:valueTo="0"
        android:valueType="floatType"/>
  </item>

  <!-- Disabled state -->
  <item>
    <objectAnimator
        android:duration="0"
        android:propertyName="translationZ"
        android:valueTo="0"
        android:valueType="floatType"/>
  </item>

</selector>
